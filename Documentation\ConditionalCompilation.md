# Conditional Compilation System

This document describes the conditional compilation system implemented for the multi-device switch prototype project.

## Overview

The conditional compilation system allows the same codebase to support multiple device models with different hardware configurations, automatically including only the necessary code for each device type.

## Device Model Selection

To select a device model, uncomment ONE of the following lines in `DeviceConfig.h`:

```cpp
// #define DEVICE_MODEL_1_SWITCH
// #define DEVICE_MODEL_2_SWITCH
#define DEVICE_MODEL_3_SWITCH        // Currently active
// #define DEVICE_MODEL_4_SWITCH
// #define DEVICE_MODEL_COOLER_CONTROL
// #define DEVICE_MODEL_SCENARIO_KEY
```

## Automatic Feature Detection

Based on the selected device model, the system automatically sets feature flags:

### Hardware Control Method
- `USE_SHIFT_REGISTERS` - Uses 74HC595 shift registers for control
- `!USE_SHIFT_REGISTERS` - Uses direct GPIO pin control

### Platform Detection
- `IS_ESP32` - ESP32-based device (cooler control)
- `!IS_ESP32` - ESP8266-based device (all others)

### Hardware Features
- `HAS_RELAYS` - Devi<PERSON> has physical relays
- `HAS_TEMPERATURE_SENSOR` - SHT30 temperature sensor (cooler only)
- `HAS_RF_RECEIVER` - RXB22 RF receiver (cooler only)
- `HAS_BUZZER` - Buzzer for notifications (cooler only)
- `HAS_FULL_COLOR_RGB` - Full 0-255 RGB range (ESP32 only)
- `HAS_COLOR_CYCLE_MODE` - Color cycling capability (ESP32 only)

## Compilation Flags

The system sets compilation flags for selective feature inclusion:

### Manager Compilation
- `COMPILE_SHIFT_REGISTER_MANAGER` - Include ShiftRegisterManager
- `COMPILE_DIRECT_PIN_MANAGER` - Include DirectPinManager

### Platform Features
- `COMPILE_ESP32_FEATURES` - ESP32-specific code
- `COMPILE_ESP32_TOUCH` - ESP32 capacitive touch
- `COMPILE_ESP32_PWM` - ESP32 PWM for full-color RGB
- `COMPILE_ESP8266_FEATURES` - ESP8266-specific code

### Device-Specific Features
- `COMPILE_TEMPERATURE_SENSOR` - SHT30 sensor code
- `COMPILE_RF_RECEIVER` - RXB22 receiver code
- `COMPILE_BUZZER` - Buzzer control code
- `COMPILE_FULL_COLOR_RGB` - Full-color RGB management
- `COMPILE_COLOR_CYCLE` - Color cycling functionality

### Memory Optimization
- `OPTIMIZE_MEMORY_USAGE` - ESP8266 memory optimizations
- `MINIMAL_LOGGING` - Reduced logging for ESP8266
- `ENABLE_DETAILED_LOGGING` - Full logging for ESP32
- `ENABLE_ADVANCED_FEATURES` - Advanced features for ESP32

## Usage Examples

### Conditional Manager Inclusion
```cpp
// In DeviceManager.h
#if USE_SHIFT_REGISTERS
    #include "ShiftRegisterManager.h"
    ShiftRegisterManager _shiftRegister;
#else
    #include "DirectPinManager.h"
    DirectPinManager _directPinManager;
#endif
```

### Platform-Specific Code
```cpp
// In TouchSensorManager.h - Unified digital touch approach for both platforms
// Both ESP32 and ESP8266 use digital touch sensors (active LOW)
return digitalRead(_touchPins[switchIndex]) == LOW;
```

### Feature-Specific Compilation
```cpp
// Only compile if device has temperature sensor
#ifdef COMPILE_TEMPERATURE_SENSOR
    #include "SHT30TemperatureSensor.h"
    SHT30TemperatureSensor tempSensor;
#endif
```

## Important Note: Boolean vs Existence Checks

When using conditional compilation with device configuration flags:

- **Use `#if` for boolean flags**: `#if HAS_FULL_COLOR_RGB`, `#if USE_SHIFT_REGISTERS`
  - These are defined as `true` or `false`, so `#ifdef` would always be true
- **Use `#ifdef` for existence flags**: `#ifdef IS_ESP32`, `#ifdef COMPILE_TEMPERATURE_SENSOR`
  - These are either defined or not defined (no boolean value)

## Device Model Configurations

### ESP8266 Devices (Shift Register Control)
- **1-Switch**: Single relay + RGB, shift register
- **2-Switch**: Dual relay + RGB, shift register  
- **3-Switch**: Triple relay + RGB, shift register
- **4-Switch**: Quad relay + RGB, shift register
- **Scenario Key**: 4 RGB keys, no relays, shift register

### ESP32 Devices (Direct Pin Control)
- **Cooler Control**: Triple relay + full-color RGB + sensors, direct pins

## Memory Optimization

### ESP8266 Optimizations
- Minimal logging to save RAM
- Conditional feature compilation
- Optimized string usage
- Reduced buffer sizes

### ESP32 Advantages
- Full feature set enabled
- Detailed logging
- Advanced RGB features
- Multiple sensor support

## Benefits

1. **Single Codebase**: Same code works for all device models
2. **Memory Efficient**: Only necessary code is compiled
3. **Platform Optimized**: Uses best features of each platform
4. **Maintainable**: Easy to add new device models
5. **Scalable**: Framework ready for future devices
