# IDE and Editor files
.vscode/
*.swp
*.swo
*~

# Backup files
/BACKUP/
*.bak
*.backup

# Build and Compiler Output
/build/
build/
*.bin
*.elf
*.map
*.hex
*.eep
*.lss
*.sym
*.o
*.a
*.so
*.dll
*.exe

# Arduino/ESP specific build files
*.bootloader.bin
*.merged.bin
*.partitions.bin
build.options.json
build_opt.h
compile_commands.json
includes.cache
libraries.cache
partitions.csv
sdkconfig
file_opts

# Temporary and cache files
*.tmp
*.temp
*.cache
*~
.DS_Store
Thumbs.db

# Core and sketch build directories
core/
sketch/
libraries/

# Platform specific build directories
esp32/
esp8266/
esp8266.*/

# Server files (if not needed in repo)
/server/

# Documentation (specific file)
/Documentation/Cooler.txt

# Log files
*.log

# Arduino CLI cache
.arduino15/
arduino_cache/
.arduino-cli/
build_cache/

# Compiler toolchains and binaries
tools/
toolchain/
xtensa-*/
riscv32-*/
arm-*/
avr-*/
gcc-*/
clang-*/
/scripts

# Arduino IDE and CLI installations
arduino/
arduino-cli/
arduino-cli.exe
arduino-builder/
arduino-builder.exe

# ESP-IDF toolchain
esp-idf/
.espressif/
idf_tools/

# Platform.io
.pio/
.pioenvs/
.piolibdeps/

# Compiler executables
*.exe
gcc
g++
clang
clang++
ld
ar
objcopy
objdump
size
nm

# Cross-compiler prefixes
xtensa-lx106-elf-*
xtensa-esp32-elf-*
riscv32-esp-elf-*
arm-none-eabi-*
avr-*

# Toolchain packages and archives
*.tar.gz
*.tar.bz2
*.tar.xz
*.zip
packages/
staging/

# Root folder compiler/build related files
arduino-cli.yaml
setup_arduino_cli.ps1
*.ps1
*.bat
*.cmd
*.sh

# Node.js/npm files (for scripts)
node_modules/
package-lock.json
yarn.lock

# Python cache and virtual environments
__pycache__/
*.pyc
*.pyo
*.pyd
venv/
env/
.env

# Server and development files
/server/
*.py
start_server.*
update_info.json

# Project specific files that might be generated
PROJECT_REORGANIZATION.md
ARDUINO_CLI_SETUP.md