/*
 * GPIO Pin Test Program
 * 
 * This program systematically tests all GPIO pins by powering them one by one
 * and printing which pin is currently active. It's designed to work with both
 * ESP8266 and ESP32 platforms and adapts to your device configuration.
 * 
 * Usage:
 * 1. Upload this code to your device
 * 2. Open Serial Monitor at 115200 baud
 * 3. Watch as each pin is activated for 2 seconds
 * 4. Use a multimeter or LED to verify which physical pin is active
 * 
 * Author: GPIO Test Generator
 * Date: 2025-07-27
 */

#include <Arduino.h>

// Include your device configuration
#include "../UniversalSwitchControl/DeviceConfig.h"

// Test configuration
#define TEST_DELAY_MS 2000        // 2 seconds per pin
#define SERIAL_BAUD_RATE 115200   // Serial monitor baud rate
#define TEST_VOLTAGE HIGH         // Voltage level to test (HIGH = 3.3V)

// GPIO pin ranges for different platforms
#ifdef IS_ESP32
  // ESP32 GPIO pins (excluding restricted pins)
  const uint8_t TEST_PINS[] = {
    0, 2, 4, 5, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 25, 26, 27, 32, 33
  };
  const char* PLATFORM_NAME = "ESP32";
#else
  // ESP8266 GPIO pins (excluding restricted pins like GPIO6-11 for flash)
  const uint8_t TEST_PINS[] = {
    0, 1, 2, 3, 4, 5, 12, 13, 14, 15, 16
  };
  const char* PLATFORM_NAME = "ESP8266";
#endif

const uint8_t NUM_TEST_PINS = sizeof(TEST_PINS) / sizeof(TEST_PINS[0]);

// Current test state
uint8_t currentPinIndex = 0;
unsigned long lastTestTime = 0;
bool testActive = false;

void setup() {
  // Initialize serial communication
  Serial.begin(SERIAL_BAUD_RATE);
  delay(2000); // Wait for serial monitor to connect
  
  // Print startup information
  Serial.println("========================================");
  Serial.println("GPIO Pin Test Program");
  Serial.println("========================================");
  Serial.print("Platform: ");
  Serial.println(PLATFORM_NAME);
  Serial.print("Device Model: ");
  Serial.println(DEVICE_TYPE);
  Serial.print("Total pins to test: ");
  Serial.println(NUM_TEST_PINS);
  Serial.println("========================================");
  
  // Print device-specific pin information
  printDeviceConfiguration();
  
  Serial.println("========================================");
  Serial.println("Starting GPIO test in 3 seconds...");
  Serial.println("Each pin will be HIGH for 2 seconds");
  Serial.println("Use a multimeter or LED to verify pins");
  Serial.println("========================================");
  
  delay(3000);
  
  // Initialize all test pins as outputs and set them LOW
  for (uint8_t i = 0; i < NUM_TEST_PINS; i++) {
    pinMode(TEST_PINS[i], OUTPUT);
    digitalWrite(TEST_PINS[i], LOW);
  }
  
  Serial.println("Test starting now...\n");
  lastTestTime = millis();
  testActive = true;
}

void loop() {
  unsigned long currentTime = millis();
  
  if (testActive && (currentTime - lastTestTime >= TEST_DELAY_MS)) {
    // Turn off previous pin
    if (currentPinIndex > 0) {
      digitalWrite(TEST_PINS[currentPinIndex - 1], LOW);
      Serial.print("Pin GPIO");
      Serial.print(TEST_PINS[currentPinIndex - 1]);
      Serial.println(" -> OFF");
    } else if (currentPinIndex == 0 && lastTestTime > 0) {
      // Turn off the last pin when starting over
      digitalWrite(TEST_PINS[NUM_TEST_PINS - 1], LOW);
      Serial.print("Pin GPIO");
      Serial.print(TEST_PINS[NUM_TEST_PINS - 1]);
      Serial.println(" -> OFF");
    }
    
    // Check if we've tested all pins
    if (currentPinIndex >= NUM_TEST_PINS) {
      Serial.println("\n========================================");
      Serial.println("All pins tested! Restarting cycle...");
      Serial.println("========================================\n");
      currentPinIndex = 0;
    }
    
    // Turn on current pin
    uint8_t currentPin = TEST_PINS[currentPinIndex];
    digitalWrite(currentPin, TEST_VOLTAGE);
    
    Serial.print("Pin GPIO");
    Serial.print(currentPin);
    Serial.print(" -> ON (");
    Serial.print(currentPinIndex + 1);
    Serial.print("/");
    Serial.print(NUM_TEST_PINS);
    Serial.print(") - ");
    
    // Print device-specific information about this pin
    printPinInfo(currentPin);
    
    currentPinIndex++;
    lastTestTime = currentTime;
  }
  
  // Small delay to prevent excessive CPU usage
  delay(10);
}

void printDeviceConfiguration() {
  Serial.println("Device Configuration:");
  
#if USE_SHIFT_REGISTERS
  Serial.println("- Uses Shift Registers");
  Serial.print("  Data Pin: GPIO");
  Serial.println(SR_DATA_PIN);
  Serial.print("  Clock Pin: GPIO");
  Serial.println(SR_CLOCK_PIN);
  Serial.print("  Latch Pin: GPIO");
  Serial.println(SR_LATCH_PIN);
#else
  Serial.println("- Uses Direct Pin Control");
#endif

#if HAS_RELAYS
  Serial.print("- Relay Count: ");
  Serial.println(SWITCH_COUNT);
  
#if !USE_SHIFT_REGISTERS
  Serial.print("- Relay Pins: ");
  for (int i = 0; i < SWITCH_COUNT; i++) {
    Serial.print("GPIO");
    Serial.print(RELAY_PIN_NUMBERS[i]);
    if (i < SWITCH_COUNT - 1) Serial.print(", ");
  }
  Serial.println();
  
  Serial.print("- RGB Red Pins: ");
  for (int i = 0; i < SWITCH_COUNT; i++) {
    Serial.print("GPIO");
    Serial.print(RED_PIN_NUMBERS[i]);
    if (i < SWITCH_COUNT - 1) Serial.print(", ");
  }
  Serial.println();
  
  Serial.print("- RGB Green Pins: ");
  for (int i = 0; i < SWITCH_COUNT; i++) {
    Serial.print("GPIO");
    Serial.print(GREEN_PIN_NUMBERS[i]);
    if (i < SWITCH_COUNT - 1) Serial.print(", ");
  }
  Serial.println();
  
  Serial.print("- RGB Blue Pins: ");
  for (int i = 0; i < SWITCH_COUNT; i++) {
    Serial.print("GPIO");
    Serial.print(BLUE_PIN_NUMBERS[i]);
    if (i < SWITCH_COUNT - 1) Serial.print(", ");
  }
  Serial.println();
#endif
#endif

  Serial.print("- Touch Pins: ");
  for (int i = 0; i < SWITCH_COUNT; i++) {
    Serial.print("GPIO");
    Serial.print(TOUCH_PIN_NUMBERS[i]);
    if (i < SWITCH_COUNT - 1) Serial.print(", ");
  }
  Serial.println();

#ifdef DEVICE_MODEL_COOLER_CONTROL
  Serial.print("- Temperature Sensor SDA: GPIO");
  Serial.println(TEMP_SENSOR_SDA_PIN);
  Serial.print("- Temperature Sensor SCL: GPIO");
  Serial.println(TEMP_SENSOR_SCL_PIN);
  Serial.print("- RF Receiver: GPIO");
  Serial.println(RF_RECEIVER_PIN);
  Serial.print("- Buzzer: GPIO");
  Serial.println(BUZZER_PIN);
#endif
}

void printPinInfo(uint8_t pin) {
  bool pinIdentified = false;
  
#if USE_SHIFT_REGISTERS
  if (pin == SR_DATA_PIN) {
    Serial.print("Shift Register DATA pin");
    pinIdentified = true;
  } else if (pin == SR_CLOCK_PIN) {
    Serial.print("Shift Register CLOCK pin");
    pinIdentified = true;
  } else if (pin == SR_LATCH_PIN) {
    Serial.print("Shift Register LATCH pin");
    pinIdentified = true;
  }
#else
  // Check if it's a relay pin
  for (int i = 0; i < SWITCH_COUNT; i++) {
    if (pin == RELAY_PIN_NUMBERS[i]) {
      Serial.print("Relay ");
      Serial.print(i + 1);
      Serial.print(" control pin");
      pinIdentified = true;
      break;
    }
  }
  
  // Check if it's an RGB pin
  if (!pinIdentified) {
    for (int i = 0; i < SWITCH_COUNT; i++) {
      if (pin == RED_PIN_NUMBERS[i]) {
        Serial.print("Switch ");
        Serial.print(i + 1);
        Serial.print(" RED LED pin");
        pinIdentified = true;
        break;
      } else if (pin == GREEN_PIN_NUMBERS[i]) {
        Serial.print("Switch ");
        Serial.print(i + 1);
        Serial.print(" GREEN LED pin");
        pinIdentified = true;
        break;
      } else if (pin == BLUE_PIN_NUMBERS[i]) {
        Serial.print("Switch ");
        Serial.print(i + 1);
        Serial.print(" BLUE LED pin");
        pinIdentified = true;
        break;
      }
    }
  }
#endif
  
  // Check if it's a touch pin
  if (!pinIdentified) {
    for (int i = 0; i < SWITCH_COUNT; i++) {
      if (pin == TOUCH_PIN_NUMBERS[i]) {
        Serial.print("Touch sensor ");
        Serial.print(i + 1);
        Serial.print(" pin");
        pinIdentified = true;
        break;
      }
    }
  }
  
#ifdef DEVICE_MODEL_COOLER_CONTROL
  // Check cooler-specific pins
  if (!pinIdentified) {
    if (pin == TEMP_SENSOR_SDA_PIN) {
      Serial.print("Temperature sensor SDA pin");
      pinIdentified = true;
    } else if (pin == TEMP_SENSOR_SCL_PIN) {
      Serial.print("Temperature sensor SCL pin");
      pinIdentified = true;
    } else if (pin == RF_RECEIVER_PIN) {
      Serial.print("RF receiver pin");
      pinIdentified = true;
    } else if (pin == BUZZER_PIN) {
      Serial.print("Buzzer pin");
      pinIdentified = true;
    }
  }
#endif
  
  if (!pinIdentified) {
    Serial.print("General purpose GPIO");
  }
  
  Serial.println();
}
