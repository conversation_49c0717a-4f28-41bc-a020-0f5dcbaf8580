# GPIO Test Programs

This folder contains test programs to help you identify and verify GPIO pin functionality on your ESP8266/ESP32 devices.

## GPIO_Test.ino

A comprehensive GPIO pin testing program that systematically powers each GPIO pin one by one and prints detailed information about which pin is currently active.

### Features

- **Platform Detection**: Automatically detects ESP8266 vs ESP32 and uses appropriate pin ranges
- **Device Configuration Aware**: Reads your DeviceConfig.h and identifies device-specific pins
- **Pin Identification**: Shows what each pin is used for (relay, RGB LED, touch sensor, etc.)
- **Safe Pin Selection**: Only tests safe GPIO pins, avoiding flash memory and other restricted pins
- **Continuous Cycling**: Continuously cycles through all pins for easy identification

### How to Use

1. **Upload the Program**:
   - Open `GPIO_Test.ino` in Arduino IDE
   - Select your board (ESP8266 or ESP32)
   - Upload to your device

2. **Open Serial Monitor**:
   - Set baud rate to 115200
   - Watch the output as each pin is activated

3. **Verify Pins Physically**:
   - Use a multimeter to measure voltage on pins (should be 3.3V when active)
   - Or connect an LED with appropriate resistor to see visual indication
   - Each pin stays HIGH for 2 seconds before moving to the next

### Example Output

```
========================================
GPIO Pin Test Program
========================================
Platform: ESP32
Device Model: CoolerControl
Total pins to test: 20
========================================
Device Configuration:
- Uses Direct Pin Control
- Relay Count: 3
- Relay Pins: GPIO5, GPIO13, GPIO14
- RGB Red Pins: GPIO27, GPIO18, GPIO2
- RGB Green Pins: GPIO25, GPIO19, GPIO16
- RGB Blue Pins: GPIO26, GPIO17, GPIO23
- Touch Pins: GPIO12, GPIO15, GPIO21
- Temperature Sensor SDA: GPIO25
- Temperature Sensor SCL: GPIO26
- RF Receiver: GPIO27
- Buzzer: GPIO14
========================================
Starting GPIO test in 3 seconds...
Each pin will be HIGH for 2 seconds
Use a multimeter or LED to verify pins
========================================
Test starting now...

Pin GPIO0 -> ON (1/20) - General purpose GPIO
Pin GPIO0 -> OFF
Pin GPIO2 -> ON (2/20) - Switch 3 BLUE LED pin
Pin GPIO2 -> OFF
Pin GPIO4 -> ON (3/20) - General purpose GPIO
Pin GPIO4 -> OFF
Pin GPIO5 -> ON (4/20) - Relay 1 control pin
Pin GPIO5 -> OFF
...
```

### Pin Safety

The program only tests safe GPIO pins:

**ESP32 Safe Pins**: 0, 2, 4, 5, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 25, 26, 27, 32, 33

**ESP8266 Safe Pins**: 0, 1, 2, 3, 4, 5, 12, 13, 14, 15, 16

Avoided pins:
- ESP32: GPIO 6-11 (flash memory), GPIO 1, 3 (UART), GPIO 34-39 (input only)
- ESP8266: GPIO 6-11 (flash memory), GPIO 9-10 (flash)

### Customization

You can modify the test program:

- **Change test duration**: Modify `TEST_DELAY_MS` (default: 2000ms)
- **Change voltage level**: Modify `TEST_VOLTAGE` (default: HIGH)
- **Add/remove pins**: Modify the `TEST_PINS` array
- **Change serial baud**: Modify `SERIAL_BAUD_RATE` (default: 115200)

### Troubleshooting

1. **No serial output**: Check baud rate is set to 115200
2. **Device resets**: Some pins might be connected to reset circuits
3. **No voltage on pin**: Pin might be input-only or have external pull-down
4. **Unexpected behavior**: Pin might be used by internal systems

### Device-Specific Notes

- **Cooler Control (ESP32)**: Has additional pins for temperature sensor, RF receiver, and buzzer
- **Switch Models (ESP8266)**: Use shift registers, so only control pins will show direct voltage
- **Scenario Key**: No relay pins, only RGB and touch sensor pins

This test program helps you verify your hardware connections and identify any wiring issues before deploying your main application.
