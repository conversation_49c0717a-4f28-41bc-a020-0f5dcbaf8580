#ifndef TOUCH_SENSOR_MANAGER_H
#define TOUCH_SENSOR_MANAGER_H

#include <Arduino.h>
#include "DeviceConfig.h"

class TouchSensorManager
{
private:
    const uint8_t _touchPins[MAX_SWITCHES] = TOUCH_PINS; // Configuration-defined pins
    bool _lastTouchState[MAX_SWITCHES];                  // Last touch state for debouncing
    unsigned long _lastTouchTime[MAX_SWITCHES];          // Last touch time for debouncing
    unsigned long _debounceDelay;                        // Debounce delay in milliseconds
    uint8_t _switchCount;                                // Number of active switches

    // Callback function pointer for touch events
    void (*_touchCallback)(uint8_t switchIndex);

    // Reset functionality - hold configured reset sensors for reset (leaves feedback sensor visible for flashing)
    bool _resetSensorsPressed;
    unsigned long _resetSensorsPressStartTime;
    bool _resetTriggered;
    static const unsigned long RESET_HOLD_TIME = 5000; // 5 seconds to trigger reset

public:
    TouchSensorManager(uint8_t switchCount = SWITCH_COUNT)
        : _switchCount(switchCount), _debounceDelay(50), _touchCallback(nullptr),
          _resetSensorsPressed(false), _resetSensorsPressStartTime(0), _resetTriggered(false)
    {
        // Initialize state arrays
        for (int i = 0; i < MAX_SWITCHES; i++)
        {
            _lastTouchState[i] = false;
            _lastTouchTime[i] = 0;
        }

        Serial.print(DEVICE_TYPE);
        Serial.println(" TouchSensorManager constructor - hardware pins:");
        for (int i = 0; i < _switchCount; i++)
        {
            Serial.print("  Pin ");
            Serial.print(i + 1);
            Serial.print(": ");
            Serial.println(_touchPins[i]);
        }
    }

    // Initialize touch sensors
    void begin()
    {
        // Initialize touch sensor pins (digital touch sensors for both platforms)
        for (int i = 0; i < _switchCount; i++)
        {
            // Both ESP32 and ESP8266 use digital touch sensors
            pinMode(_touchPins[i], INPUT_PULLUP);
            Serial.print(DEVICE_TYPE);
            Serial.print(" Digital touch sensor ");
            Serial.print(i + 1);
            Serial.print(" initialized on pin ");
            Serial.println(_touchPins[i]);
        }

        Serial.print(DEVICE_TYPE);
        Serial.println(" Touch Sensor Manager initialized");
    }

    // Set callback function for touch events
    void setTouchCallback(void (*callback)(uint8_t switchIndex))
    {
        _touchCallback = callback;
    }

    // Get touch pin for a specific switch
    uint8_t getTouchPin(uint8_t switchIndex)
    {
        if (switchIndex < _switchCount)
        {
            return _touchPins[switchIndex];
        }
        return 0;
    }

    // Get debounce delay
    unsigned long getDebounceDelay()
    {
        return _debounceDelay;
    }

    // Read touch sensor state (digital touch sensors for both platforms)
    bool readTouchSensor(uint8_t switchIndex)
    {
        if (switchIndex < _switchCount)
        {
            // Both ESP32 and ESP8266 use digital touch sensors (active LOW)
            return digitalRead(_touchPins[switchIndex]) == LOW;
        }
        return false;
    }

    // Handle touch sensors - call this in main loop
    void handleTouchSensors()
    {
        unsigned long currentTime = millis();

        // Check if configured reset sensors are currently pressed
        bool resetSensorsPressed = false;
        if (_switchCount > RESET_TOUCH_1 && _switchCount > RESET_TOUCH_2)
        {
            resetSensorsPressed = readTouchSensor(RESET_TOUCH_1) && readTouchSensor(RESET_TOUCH_2);
        }

        // Handle reset functionality
        handleResetFunctionality(resetSensorsPressed, currentTime);

        for (uint8_t i = 0; i < _switchCount; i++)
        {
            bool currentTouch = readTouchSensor(i);

            // Debug: Print touch state changes
            if (currentTouch != _lastTouchState[i])
            {
                Serial.print("Touch sensor ");
                Serial.print(i + 1);
                Serial.print(" (pin ");
                Serial.print(_touchPins[i]);
                Serial.print(") state changed to: ");
                Serial.println(currentTouch ? "PRESSED" : "RELEASED");
            }

            // Check for touch press (transition from not touched to touched)
            // Only trigger individual callbacks if reset is not in progress
            if (currentTouch && !_lastTouchState[i] &&
                (currentTime - _lastTouchTime[i] > _debounceDelay) &&
                !_resetSensorsPressed) // Don't trigger individual callbacks during reset sequence
            {
                _lastTouchTime[i] = currentTime;

                Serial.print(DEVICE_TYPE);
                Serial.print(" Touch sensor ");
                Serial.print(i + 1);
                Serial.print(" (pin ");
                Serial.print(_touchPins[i]);
                Serial.println(") pressed - TRIGGERING CALLBACK");

                // Call callback function if set
                if (_touchCallback)
                {
                    Serial.println("Calling touch callback...");
                    _touchCallback(i);
                }
                else
                {
                    Serial.println("ERROR: No touch callback set!");
                }
            }

            _lastTouchState[i] = currentTouch;
        }
    }

    // Get switch count
    uint8_t getSwitchCount()
    {
        return _switchCount;
    }

    // Set switch count
    void setSwitchCount(uint8_t count)
    {
        if (count <= 3)
        {
            _switchCount = count;
        }
    }

    // Test all touch sensors (for debugging)
    void testTouchSensors()
    {
        Serial.println("Testing 3-Relay touch sensors...");
        for (int i = 0; i < _switchCount; i++)
        {
            bool state = readTouchSensor(i);
            Serial.print("Touch sensor ");
            Serial.print(i + 1);
            Serial.print(" (pin ");
            Serial.print(_touchPins[i]);
            Serial.print("): ");
            Serial.println(state ? "PRESSED" : "NOT PRESSED");
        }
    }

    // Check if reset is in progress (for external monitoring)
    bool isResetInProgress()
    {
        return _resetSensorsPressed;
    }

    // Get remaining time until reset (in milliseconds)
    unsigned long getResetTimeRemaining()
    {
        if (_resetSensorsPressed && !_resetTriggered)
        {
            unsigned long elapsed = millis() - _resetSensorsPressStartTime;
            if (elapsed < RESET_HOLD_TIME)
            {
                return RESET_HOLD_TIME - elapsed;
            }
        }
        return 0;
    }

private:
    // Handle the reset functionality when sensors 1 and 3 are pressed
    void handleResetFunctionality(bool resetSensorsCurrentlyPressed, unsigned long currentTime)
    {
        if (resetSensorsCurrentlyPressed && !_resetSensorsPressed)
        {
            // Reset sensors just became pressed
            _resetSensorsPressed = true;
            _resetSensorsPressStartTime = currentTime;
            _resetTriggered = false;
            Serial.print("=== TOUCH SENSORS ");
            Serial.print(RESET_TOUCH_1 + 1);
            Serial.print(" & ");
            Serial.print(RESET_TOUCH_2 + 1);
            Serial.println(" PRESSED ===");
            Serial.println("Hold for 5 seconds to reset device...");
            Serial.print("Watch sensor ");
            Serial.print(RESET_FEEDBACK_SWITCH + 1);
            Serial.println(" RGB light flash red!");
        }
        else if (!resetSensorsCurrentlyPressed && _resetSensorsPressed)
        {
            // Reset sensors are not pressed anymore
            _resetSensorsPressed = false;
            _resetTriggered = false;
            Serial.print("Reset sequence cancelled - sensors ");
            Serial.print(RESET_TOUCH_1 + 1);
            Serial.print(" & ");
            Serial.print(RESET_TOUCH_2 + 1);
            Serial.println(" not both pressed");
        }
        else if (_resetSensorsPressed && !_resetTriggered)
        {
            // Reset sensors are still pressed, check if enough time has passed
            unsigned long holdTime = currentTime - _resetSensorsPressStartTime;

            // Show countdown every second
            static unsigned long lastCountdownTime = 0;
            if (currentTime - lastCountdownTime >= 1000)
            {
                lastCountdownTime = currentTime;
                unsigned long remainingTime = (RESET_HOLD_TIME - holdTime) / 1000;
                if (remainingTime > 0)
                {
                    Serial.print("Reset in ");
                    Serial.print(remainingTime);
                    Serial.println(" seconds...");
                }
            }

            if (holdTime >= RESET_HOLD_TIME)
            {
                _resetTriggered = true;
                Serial.println("=== DEVICE RESET TRIGGERED ===");
                Serial.print("Touch sensors ");
                Serial.print(RESET_TOUCH_1 + 1);
                Serial.print(" & ");
                Serial.print(RESET_TOUCH_2 + 1);
                Serial.println(" held for 5 seconds");
                Serial.print("Restarting ");
                Serial.print(DEVICE_TYPE);
                Serial.println(" in 2 seconds...");
                delay(2000);
                ESP.restart();
            }
        }
    }
};

#endif // TOUCH_SENSOR_MANAGER_H
